package com.sanythadmin.project.workstudy.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.sanythadmin.common.core.web.PageResult;
import com.sanythadmin.common.system.param.UserInfoParam;
import com.sanythadmin.project.workflow.entity.WorkflowApprovalNodeRecord;
import com.sanythadmin.project.workflow.param.WorkflowApprovalNodeParam;
import com.sanythadmin.project.workstudy.entity.QgzxJobApplication;
import com.sanythadmin.project.workstudy.entity.QgzxStudentApply;
import com.sanythadmin.project.workstudy.param.QgzxJobApplicationParam;
import com.sanythadmin.project.workstudy.param.QgzxStudentApplyParam;
import com.sanythadmin.project.workstudy.vo.QgzxJobApplicationVO;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

/**
 * 学生岗位申请Service
 *
 * <AUTHOR>
 * @since 2025-07-10 15:30:43
 */
public interface QgzxStudentApplyService extends IService<QgzxStudentApply> {

    PageResult<QgzxJobApplicationVO> pageByApplicantList(QgzxJobApplicationParam param);
    List<QgzxJobApplicationVO> listApplicantList(QgzxJobApplicationParam param);
    String checkApplyTime(QgzxJobApplication jobApplication);
    String checkLimit(String xgh, QgzxJobApplication jobApplication);
    String checkIsEdit(String id);
    void editByApplicant(QgzxStudentApply studentApply);
    void delete(String... id);
    void deleteByApplicant(String... id);
    PageResult<QgzxStudentApply> pageByStudent(QgzxStudentApplyParam param, UserInfoParam userInfoParam);
    List<QgzxStudentApply> listByStudent(QgzxStudentApplyParam param, UserInfoParam userInfoParam);
    PageResult<QgzxStudentApply> pageByEmployer(QgzxStudentApplyParam param);
    QgzxStudentApply getDetail(String id);
    PageResult<QgzxStudentApply> pageApprovalList(QgzxStudentApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam);
    List<QgzxStudentApply> listApprovalList(QgzxStudentApplyParam param, UserInfoParam userInfoParam, WorkflowApprovalNodeParam approvalNodeParam);
    CompletableFuture<String> approve(WorkflowApprovalNodeRecord record,QgzxStudentApply studentApply, Executor executor);
    PageResult<QgzxStudentApply> pageForInterview(QgzxStudentApplyParam param);
    void applyAdjustment(String studentApplyId, String targetJobId, String reason);
    List<QgzxStudentApply> getAdjustmentHistory(String studentApplyId);
    String validateAdjustment(String studentApplyId, String targetJobId);
}